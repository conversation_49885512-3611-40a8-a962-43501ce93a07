// Main Dashboard JavaScript

class Dashboard {
    constructor() {
        this.isInitialized = false;
        this.marketCards = new Map();
        this.currentSymbol = 'AAPL';
        this.updateInterval = null;
    }

    // Initialize dashboard
    async init() {
        try {
            ConfigUtils.debug('Initializing dashboard...');
            
            // Initialize chart
            this.initChart();
            
            // Set up market cards
            this.initMarketCards();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Start live data updates
            this.startLiveUpdates();
            
            // Initialize pattern detection
            if (ConfigUtils.isFeatureEnabled('PATTERN_DETECTION')) {
                this.initPatternDetection();
            }
            
            this.isInitialized = true;
            ConfigUtils.debug('Dashboard initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize dashboard:', error);
            this.showError('Failed to initialize dashboard. Please refresh the page.');
        }
    }

    // Initialize TradingView chart
    initChart() {
        // Wait for TradingView library to load
        if (typeof TradingView === 'undefined') {
            setTimeout(() => this.initChart(), 1000);
            return;
        }

        chartManager.init('tradingview_chart');
    }

    // Initialize market data cards
    initMarketCards() {
        const cardElements = document.querySelectorAll('.market-card');
        
        cardElements.forEach(card => {
            const symbol = card.dataset.symbol;
            this.marketCards.set(symbol, {
                element: card,
                priceElement: card.querySelector(`#price-${symbol}`),
                changeElement: card.querySelector(`#change-${symbol}`)
            });

            // Add click handler to change chart symbol
            card.addEventListener('click', () => {
                this.changeSymbol(symbol);
            });
        });
    }

    // Set up event listeners
    setupEventListeners() {
        // Chart symbol buttons
        const symbolButtons = document.querySelectorAll('[data-symbol]');
        symbolButtons.forEach(button => {
            if (button.classList.contains('btn')) {
                button.addEventListener('click', (e) => {
                    const symbol = e.target.dataset.symbol;
                    this.changeChartSymbol(symbol);
                    
                    // Update active button
                    symbolButtons.forEach(btn => btn.classList.remove('active'));
                    e.target.classList.add('active');
                });
            }
        });

        // Listen for chart events
        document.addEventListener('chartSymbolChanged', (e) => {
            this.onChartSymbolChanged(e.detail);
        });

        // Window visibility change (pause updates when tab is hidden)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseUpdates();
            } else {
                this.resumeUpdates();
            }
        });

        // Error handling for API failures
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            this.showError('An error occurred while fetching data.');
        });
    }

    // Start live data updates
    startLiveUpdates() {
        // Subscribe to stock data
        CONFIG.SYMBOLS.STOCKS.forEach(stock => {
            marketData.subscribe(stock.symbol, (data, error) => {
                if (error) {
                    this.handleDataError(stock.symbol, error);
                } else {
                    this.updateMarketCard(stock.symbol, data);
                }
            });
        });

        // Subscribe to crypto data
        CONFIG.SYMBOLS.CRYPTO.forEach(crypto => {
            marketData.subscribe(crypto.id, (data, error) => {
                if (error) {
                    this.handleDataError(crypto.id, error);
                } else {
                    this.updateMarketCard(crypto.id, data);
                }
            });
        });
    }

    // Update market card with new data
    updateMarketCard(symbol, data) {
        const card = this.marketCards.get(symbol);
        if (!card) return;

        try {
            // Update price
            const price = data.price || data.close;
            card.priceElement.textContent = ConfigUtils.formatCurrency(price);

            // Update change
            const change = data.change || data.change24h || 0;
            const changePercent = data.changePercent || (data.change24h || 0);
            
            const changeText = `${change >= 0 ? '+' : ''}${ConfigUtils.formatCurrency(change)} (${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)`;
            card.changeElement.textContent = changeText;

            // Update styling
            card.changeElement.className = `change ${change >= 0 ? 'positive' : 'negative'}`;

            // Add animation
            card.element.classList.add('fade-in');
            setTimeout(() => card.element.classList.remove('fade-in'), 500);

        } catch (error) {
            console.error(`Error updating card for ${symbol}:`, error);
        }
    }

    // Handle data errors
    handleDataError(symbol, error) {
        const card = this.marketCards.get(symbol);
        if (!card) return;

        card.priceElement.textContent = 'Error';
        card.changeElement.textContent = error.message || 'Failed to load';
        card.changeElement.className = 'change text-muted';

        ConfigUtils.debug(`Data error for ${symbol}:`, error);
    }

    // Change chart symbol
    changeChartSymbol(symbol) {
        this.currentSymbol = symbol;
        chartManager.changeSymbol(symbol);
    }

    // Handle chart symbol change
    onChartSymbolChanged(detail) {
        ConfigUtils.debug('Chart symbol changed:', detail);
        // Update any UI elements that depend on current symbol
    }

    // Initialize pattern detection
    initPatternDetection() {
        // This will be implemented in patterns.js
        if (typeof PatternDetector !== 'undefined') {
            const detector = new PatternDetector();
            detector.startDetection();
        }
    }

    // Show error message
    showError(message) {
        // Create or update error alert
        let errorAlert = document.getElementById('error-alert');
        
        if (!errorAlert) {
            errorAlert = document.createElement('div');
            errorAlert.id = 'error-alert';
            errorAlert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            errorAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            
            errorAlert.innerHTML = `
                <strong>Error:</strong> <span id="error-message"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(errorAlert);
        }

        document.getElementById('error-message').textContent = message;
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (errorAlert && errorAlert.parentNode) {
                errorAlert.remove();
            }
        }, 5000);
    }

    // Show success message
    showSuccess(message) {
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        successAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        
        successAlert.innerHTML = `
            <strong>Success:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(successAlert);
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            if (successAlert && successAlert.parentNode) {
                successAlert.remove();
            }
        }, 3000);
    }

    // Pause updates (when tab is hidden)
    pauseUpdates() {
        ConfigUtils.debug('Pausing updates');
        // Market data manager will handle this automatically
    }

    // Resume updates (when tab becomes visible)
    resumeUpdates() {
        ConfigUtils.debug('Resuming updates');
        // Market data manager will handle this automatically
    }

    // Refresh all data
    async refreshData() {
        try {
            // Clear cache
            marketData.stockAPI.cache.clear();
            marketData.cryptoAPI.cache.clear();

            // Force update all symbols
            const allSymbols = [
                ...CONFIG.SYMBOLS.STOCKS.map(s => s.symbol),
                ...CONFIG.SYMBOLS.CRYPTO.map(c => c.id)
            ];

            for (const symbol of allSymbols) {
                await marketData.updateSymbol(symbol);
            }

            this.showSuccess('Data refreshed successfully');
        } catch (error) {
            console.error('Failed to refresh data:', error);
            this.showError('Failed to refresh data');
        }
    }

    // Export data
    exportData() {
        try {
            const data = {
                timestamp: new Date().toISOString(),
                symbols: {},
                preferences: ConfigUtils.getPreferences()
            };

            // Collect current market data
            this.marketCards.forEach((card, symbol) => {
                const priceText = card.priceElement.textContent;
                const changeText = card.changeElement.textContent;
                
                if (priceText !== 'Loading...' && priceText !== 'Error') {
                    data.symbols[symbol] = {
                        price: priceText,
                        change: changeText
                    };
                }
            });

            // Create and download file
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `trading_dashboard_data_${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showSuccess('Data exported successfully');
        } catch (error) {
            console.error('Failed to export data:', error);
            this.showError('Failed to export data');
        }
    }

    // Cleanup
    destroy() {
        // Stop all updates
        marketData.cleanup();
        
        // Destroy chart
        chartManager.destroy();
        
        // Clear intervals
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.isInitialized = false;
        ConfigUtils.debug('Dashboard destroyed');
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const dashboard = new Dashboard();
    dashboard.init();
    
    // Make dashboard globally available
    window.dashboard = dashboard;
});

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (window.dashboard) {
        window.dashboard.destroy();
    }
});

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'r':
                e.preventDefault();
                if (window.dashboard) {
                    window.dashboard.refreshData();
                }
                break;
            case 'e':
                e.preventDefault();
                if (window.dashboard) {
                    window.dashboard.exportData();
                }
                break;
        }
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Dashboard };
}
