<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Dashboard Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header text-center">
                        <h3><i class="fas fa-chart-line me-2"></i>Trading Dashboard Demo</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Setup Required</h5>
                            <p>To use the full functionality of this trading dashboard, you need to:</p>
                            <ol>
                                <li>Get a free API key from <a href="https://www.alphavantage.co/support/#api-key" target="_blank">Alpha Vantage</a></li>
                                <li>Open <code>js/config.js</code> and replace <code>YOUR_ALPHA_VANTAGE_API_KEY</code> with your actual API key</li>
                                <li>Use a local server (see README.md for instructions)</li>
                            </ol>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-4 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-home fa-2x text-primary mb-2"></i>
                                        <h6>Dashboard</h6>
                                        <p class="small text-muted">Live market data and charts</p>
                                        <a href="index.html" class="btn btn-primary btn-sm">Open Dashboard</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-history fa-2x text-success mb-2"></i>
                                        <h6>Backtest</h6>
                                        <p class="small text-muted">Strategy backtesting</p>
                                        <a href="backtest.html" class="btn btn-success btn-sm">Open Backtest</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-graduation-cap fa-2x text-warning mb-2"></i>
                                        <h6>Learn</h6>
                                        <p class="small text-muted">Educational tools</p>
                                        <a href="learn.html" class="btn btn-warning btn-sm">Open Learn</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <h5>Features Included:</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>Live stock data (Alpha Vantage)</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Live crypto data (CoinGecko)</li>
                                            <li><i class="fas fa-check text-success me-2"></i>TradingView charts</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Pattern detection</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>Backtesting engine</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Educational content</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Responsive design</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Export functionality</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Demo Mode</h6>
                            <p class="mb-0">Without API keys, some features will show demo data or error messages. This is normal for the initial setup.</p>
                        </div>

                        <div class="text-center mt-4">
                            <a href="README.md" class="btn btn-outline-primary me-2">
                                <i class="fas fa-book me-1"></i>Read Documentation
                            </a>
                            <button class="btn btn-outline-secondary" onclick="testAPIs()">
                                <i class="fas fa-flask me-1"></i>Test API Connection
                            </button>
                        </div>

                        <div id="api-test-results" class="mt-3" style="display: none;">
                            <!-- API test results will appear here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    
    <script>
        async function testAPIs() {
            const resultsDiv = document.getElementById('api-test-results');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin me-2"></i>Testing API connections...
                </div>
            `;

            const results = [];

            // Test Alpha Vantage
            try {
                const stockAPI = new StockAPI();
                await stockAPI.getQuote('AAPL');
                results.push({
                    name: 'Alpha Vantage (Stocks)',
                    status: 'success',
                    message: 'Connection successful'
                });
            } catch (error) {
                results.push({
                    name: 'Alpha Vantage (Stocks)',
                    status: 'error',
                    message: error.message
                });
            }

            // Test CoinGecko
            try {
                const cryptoAPI = new CryptoAPI();
                await cryptoAPI.getPrices('bitcoin');
                results.push({
                    name: 'CoinGecko (Crypto)',
                    status: 'success',
                    message: 'Connection successful'
                });
            } catch (error) {
                results.push({
                    name: 'CoinGecko (Crypto)',
                    status: 'error',
                    message: error.message
                });
            }

            // Display results
            let html = '<div class="card"><div class="card-header"><h6>API Test Results</h6></div><div class="card-body">';
            
            results.forEach(result => {
                const iconClass = result.status === 'success' ? 'fa-check text-success' : 'fa-times text-danger';
                const alertClass = result.status === 'success' ? 'alert-success' : 'alert-danger';
                
                html += `
                    <div class="alert ${alertClass} py-2">
                        <i class="fas ${iconClass} me-2"></i>
                        <strong>${result.name}:</strong> ${result.message}
                    </div>
                `;
            });
            
            html += '</div></div>';
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
