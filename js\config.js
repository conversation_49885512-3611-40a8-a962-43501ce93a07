// Configuration file for Trading Dashboard

// API Configuration
const CONFIG = {
    // Alpha Vantage API for stock data
    ALPHA_VANTAGE: {
        BASE_URL: 'https://www.alphavantage.co/query',
        API_KEY: 'YOUR_ALPHA_VANTAGE_API_KEY', // Replace with your actual API key
        ENDPOINTS: {
            QUOTE: 'GLOBAL_QUOTE',
            INTRADAY: 'TIME_SERIES_INTRADAY',
            DAILY: 'TIME_SERIES_DAILY',
            WEEKLY: 'TIME_SERIES_WEEKLY',
            MONTHLY: 'TIME_SERIES_MONTHLY'
        }
    },
    
    // CoinGecko API for cryptocurrency data
    COINGECKO: {
        BASE_URL: 'https://api.coingecko.com/api/v3',
        ENDPOINTS: {
            SIMPLE_PRICE: '/simple/price',
            COINS_LIST: '/coins/list',
            COIN_HISTORY: '/coins/{id}/history',
            MARKET_DATA: '/coins/{id}/market_chart'
        }
    },
    
    // TradingView Widget Configuration
    TRADINGVIEW: {
        WIDGET_CONFIG: {
            autosize: true,
            symbol: "NASDAQ:AAPL",
            interval: "D",
            timezone: "Etc/UTC",
            theme: "light",
            style: "1",
            locale: "en",
            toolbar_bg: "#f1f3f6",
            enable_publishing: false,
            allow_symbol_change: true,
            container_id: "tradingview_chart",
            studies: [
                "RSI@tv-basicstudies",
                "MACD@tv-basicstudies",
                "BB@tv-basicstudies"
            ]
        }
    },
    
    // Default symbols to track
    SYMBOLS: {
        STOCKS: [
            { symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ' },
            { symbol: 'GOOGL', name: 'Alphabet Inc.', exchange: 'NASDAQ' },
            { symbol: 'MSFT', name: 'Microsoft Corp.', exchange: 'NASDAQ' },
            { symbol: 'TSLA', name: 'Tesla Inc.', exchange: 'NASDAQ' },
            { symbol: 'AMZN', name: 'Amazon.com Inc.', exchange: 'NASDAQ' }
        ],
        CRYPTO: [
            { id: 'bitcoin', symbol: 'BTC', name: 'Bitcoin' },
            { id: 'ethereum', symbol: 'ETH', name: 'Ethereum' },
            { id: 'binancecoin', symbol: 'BNB', name: 'Binance Coin' },
            { id: 'cardano', symbol: 'ADA', name: 'Cardano' },
            { id: 'solana', symbol: 'SOL', name: 'Solana' }
        ]
    },
    
    // Update intervals (in milliseconds)
    UPDATE_INTERVALS: {
        LIVE_DATA: 30000, // 30 seconds
        PATTERN_DETECTION: 60000, // 1 minute
        CHART_REFRESH: 300000 // 5 minutes
    },
    
    // Backtesting configuration
    BACKTEST: {
        DEFAULT_CAPITAL: 10000,
        DEFAULT_POSITION_SIZE: 0.25, // 25%
        COMMISSION: 0.001, // 0.1%
        SLIPPAGE: 0.0005, // 0.05%
        STRATEGIES: {
            RSI: {
                name: 'RSI Strategy',
                description: 'Buy when RSI < 30, Sell when RSI > 70',
                parameters: {
                    period: 14,
                    oversold: 30,
                    overbought: 70
                }
            },
            MA_CROSSOVER: {
                name: 'Moving Average Crossover',
                description: 'Buy when fast MA crosses above slow MA',
                parameters: {
                    fast_period: 10,
                    slow_period: 20
                }
            },
            BOLLINGER: {
                name: 'Bollinger Bands',
                description: 'Buy at lower band, sell at upper band',
                parameters: {
                    period: 20,
                    std_dev: 2
                }
            },
            MACD: {
                name: 'MACD Strategy',
                description: 'Buy on MACD bullish crossover',
                parameters: {
                    fast_period: 12,
                    slow_period: 26,
                    signal_period: 9
                }
            }
        }
    },
    
    // Candlestick pattern detection settings
    PATTERNS: {
        DETECTION_SENSITIVITY: 0.7, // 0-1 scale
        MIN_BODY_SIZE: 0.1, // Minimum body size as percentage of range
        DOJI_THRESHOLD: 0.05, // Maximum body size for doji pattern
        SHADOW_RATIO: 2.0, // Minimum shadow to body ratio for certain patterns
        ENABLED_PATTERNS: [
            'doji',
            'hammer',
            'shooting_star',
            'engulfing',
            'harami',
            'spinning_top',
            'marubozu',
            'dark_cloud_cover'
        ]
    },
    
    // Chart colors and styling
    CHART_COLORS: {
        BULLISH: '#26a69a',
        BEARISH: '#ef5350',
        VOLUME: '#42a5f5',
        MA_FAST: '#ff9800',
        MA_SLOW: '#9c27b0',
        RSI: '#2196f3',
        MACD: '#4caf50',
        SIGNAL: '#f44336',
        BOLLINGER_UPPER: '#e91e63',
        BOLLINGER_LOWER: '#e91e63',
        BOLLINGER_MIDDLE: '#9e9e9e'
    },
    
    // Error handling
    ERROR_MESSAGES: {
        API_LIMIT: 'API rate limit exceeded. Please try again later.',
        NETWORK_ERROR: 'Network error. Please check your connection.',
        INVALID_SYMBOL: 'Invalid symbol. Please check the symbol and try again.',
        NO_DATA: 'No data available for the selected period.',
        CALCULATION_ERROR: 'Error in calculations. Please try again.'
    },
    
    // Local storage keys
    STORAGE_KEYS: {
        API_KEYS: 'trading_dashboard_api_keys',
        USER_PREFERENCES: 'trading_dashboard_preferences',
        WATCHLIST: 'trading_dashboard_watchlist',
        BACKTEST_HISTORY: 'trading_dashboard_backtest_history'
    },
    
    // Development mode flag
    DEBUG_MODE: true,
    
    // Feature flags
    FEATURES: {
        REAL_TIME_DATA: true,
        PATTERN_DETECTION: true,
        BACKTESTING: true,
        LEARNING_MODULE: true,
        EXPORT_DATA: true,
        NOTIFICATIONS: false // Future feature
    }
};

// Utility functions for configuration
const ConfigUtils = {
    // Get API key from local storage or config
    getApiKey: function(service) {
        const stored = localStorage.getItem(CONFIG.STORAGE_KEYS.API_KEYS);
        if (stored) {
            const keys = JSON.parse(stored);
            return keys[service] || CONFIG[service].API_KEY;
        }
        return CONFIG[service].API_KEY;
    },
    
    // Set API key in local storage
    setApiKey: function(service, key) {
        const stored = localStorage.getItem(CONFIG.STORAGE_KEYS.API_KEYS) || '{}';
        const keys = JSON.parse(stored);
        keys[service] = key;
        localStorage.setItem(CONFIG.STORAGE_KEYS.API_KEYS, JSON.stringify(keys));
    },
    
    // Get user preferences
    getPreferences: function() {
        const stored = localStorage.getItem(CONFIG.STORAGE_KEYS.USER_PREFERENCES);
        return stored ? JSON.parse(stored) : {};
    },
    
    // Set user preferences
    setPreferences: function(preferences) {
        localStorage.setItem(CONFIG.STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
    },
    
    // Check if feature is enabled
    isFeatureEnabled: function(feature) {
        return CONFIG.FEATURES[feature] || false;
    },
    
    // Log debug messages
    debug: function(message, data = null) {
        if (CONFIG.DEBUG_MODE) {
            console.log(`[Trading Dashboard] ${message}`, data);
        }
    },
    
    // Format currency
    formatCurrency: function(value, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(value);
    },
    
    // Format percentage
    formatPercentage: function(value, decimals = 2) {
        return `${(value * 100).toFixed(decimals)}%`;
    },
    
    // Format large numbers
    formatNumber: function(value) {
        if (value >= 1e9) {
            return (value / 1e9).toFixed(2) + 'B';
        } else if (value >= 1e6) {
            return (value / 1e6).toFixed(2) + 'M';
        } else if (value >= 1e3) {
            return (value / 1e3).toFixed(2) + 'K';
        }
        return value.toFixed(2);
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, ConfigUtils };
}
