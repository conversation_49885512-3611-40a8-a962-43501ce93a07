// Candlestick Pattern Detection Module

class PatternDetector {
    constructor() {
        this.patterns = [];
        this.isRunning = false;
        this.detectionInterval = null;
    }

    // Start pattern detection
    startDetection() {
        if (this.isRunning) return;

        this.isRunning = true;
        this.detectionInterval = setInterval(() => {
            this.detectPatterns();
        }, CONFIG.UPDATE_INTERVALS.PATTERN_DETECTION);

        // Initial detection
        this.detectPatterns();
        ConfigUtils.debug('Pattern detection started');
    }

    // Stop pattern detection
    stopDetection() {
        if (this.detectionInterval) {
            clearInterval(this.detectionInterval);
            this.detectionInterval = null;
        }
        this.isRunning = false;
        ConfigUtils.debug('Pattern detection stopped');
    }

    // Main pattern detection function
    async detectPatterns() {
        try {
            const symbols = [...CONFIG.SYMBOLS.STOCKS.map(s => s.symbol)];
            const detectedPatterns = [];

            for (const symbol of symbols) {
                try {
                    const historicalData = await marketData.getHistoricalData(symbol, '1M', 'daily');
                    if (historicalData && historicalData.length >= 3) {
                        const patterns = this.analyzePatterns(symbol, historicalData);
                        detectedPatterns.push(...patterns);
                    }
                } catch (error) {
                    ConfigUtils.debug(`Error detecting patterns for ${symbol}:`, error);
                }
            }

            this.patterns = detectedPatterns;
            this.updatePatternDisplay();

        } catch (error) {
            console.error('Error in pattern detection:', error);
        }
    }

    // Analyze patterns in historical data
    analyzePatterns(symbol, data) {
        const patterns = [];
        const recentData = data.slice(-10); // Analyze last 10 candles

        for (let i = 2; i < recentData.length; i++) {
            const current = recentData[i];
            const previous = recentData[i - 1];
            const beforePrevious = recentData[i - 2];

            // Check for various patterns
            const detectedPattern = this.checkPatterns(current, previous, beforePrevious);
            
            if (detectedPattern) {
                patterns.push({
                    symbol: symbol,
                    type: detectedPattern.type,
                    name: detectedPattern.name,
                    signal: detectedPattern.signal,
                    confidence: detectedPattern.confidence,
                    date: current.date,
                    price: current.close,
                    description: detectedPattern.description
                });
            }
        }

        return patterns;
    }

    // Check for specific patterns
    checkPatterns(current, previous, beforePrevious) {
        const patterns = [
            this.checkDoji(current),
            this.checkHammer(current, previous),
            this.checkShootingStar(current, previous),
            this.checkEngulfing(current, previous),
            this.checkHarami(current, previous),
            this.checkSpinningTop(current),
            this.checkMarubozu(current),
            this.checkDarkCloudCover(current, previous)
        ];

        // Return the pattern with highest confidence
        return patterns
            .filter(p => p !== null)
            .sort((a, b) => b.confidence - a.confidence)[0] || null;
    }

    // Doji pattern detection
    checkDoji(candle) {
        const bodySize = Math.abs(candle.close - candle.open);
        const range = candle.high - candle.low;
        const bodyRatio = bodySize / range;

        if (bodyRatio <= CONFIG.PATTERNS.DOJI_THRESHOLD && range > 0) {
            return {
                type: 'doji',
                name: 'Doji',
                signal: 'neutral',
                confidence: 1 - bodyRatio,
                description: 'Indecision pattern - potential reversal signal'
            };
        }
        return null;
    }

    // Hammer pattern detection
    checkHammer(current, previous) {
        const bodySize = Math.abs(current.close - current.open);
        const lowerShadow = Math.min(current.open, current.close) - current.low;
        const upperShadow = current.high - Math.max(current.open, current.close);
        const range = current.high - current.low;

        if (range > 0 && lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5) {
            const isInDowntrend = previous.close < previous.open;
            
            if (isInDowntrend) {
                return {
                    type: 'hammer',
                    name: 'Hammer',
                    signal: 'bullish',
                    confidence: Math.min(lowerShadow / bodySize / 3, 0.9),
                    description: 'Bullish reversal pattern after downtrend'
                };
            }
        }
        return null;
    }

    // Shooting Star pattern detection
    checkShootingStar(current, previous) {
        const bodySize = Math.abs(current.close - current.open);
        const lowerShadow = Math.min(current.open, current.close) - current.low;
        const upperShadow = current.high - Math.max(current.open, current.close);
        const range = current.high - current.low;

        if (range > 0 && upperShadow > bodySize * 2 && lowerShadow < bodySize * 0.5) {
            const isInUptrend = previous.close > previous.open;
            
            if (isInUptrend) {
                return {
                    type: 'shooting_star',
                    name: 'Shooting Star',
                    signal: 'bearish',
                    confidence: Math.min(upperShadow / bodySize / 3, 0.9),
                    description: 'Bearish reversal pattern after uptrend'
                };
            }
        }
        return null;
    }

    // Engulfing pattern detection
    checkEngulfing(current, previous) {
        const currentBody = Math.abs(current.close - current.open);
        const previousBody = Math.abs(previous.close - previous.open);
        
        // Current candle must engulf previous candle's body
        if (currentBody > previousBody * 1.2) {
            // Bullish engulfing
            if (previous.close < previous.open && current.close > current.open &&
                current.open < previous.close && current.close > previous.open) {
                return {
                    type: 'engulfing',
                    name: 'Bullish Engulfing',
                    signal: 'bullish',
                    confidence: Math.min(currentBody / previousBody / 2, 0.9),
                    description: 'Strong bullish reversal pattern'
                };
            }
            
            // Bearish engulfing
            if (previous.close > previous.open && current.close < current.open &&
                current.open > previous.close && current.close < previous.open) {
                return {
                    type: 'engulfing',
                    name: 'Bearish Engulfing',
                    signal: 'bearish',
                    confidence: Math.min(currentBody / previousBody / 2, 0.9),
                    description: 'Strong bearish reversal pattern'
                };
            }
        }
        return null;
    }

    // Harami pattern detection
    checkHarami(current, previous) {
        const currentBody = Math.abs(current.close - current.open);
        const previousBody = Math.abs(previous.close - previous.open);
        
        // Current candle's body must be inside previous candle's body
        if (currentBody < previousBody * 0.7 &&
            Math.max(current.open, current.close) < Math.max(previous.open, previous.close) &&
            Math.min(current.open, current.close) > Math.min(previous.open, previous.close)) {
            
            const signal = previous.close > previous.open ? 'bearish' : 'bullish';
            
            return {
                type: 'harami',
                name: 'Harami',
                signal: signal,
                confidence: 1 - (currentBody / previousBody),
                description: 'Potential reversal pattern - indecision after strong move'
            };
        }
        return null;
    }

    // Spinning Top pattern detection
    checkSpinningTop(candle) {
        const bodySize = Math.abs(candle.close - candle.open);
        const upperShadow = candle.high - Math.max(candle.open, candle.close);
        const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
        const range = candle.high - candle.low;

        if (range > 0 && bodySize < range * 0.3 && 
            upperShadow > bodySize && lowerShadow > bodySize) {
            return {
                type: 'spinning_top',
                name: 'Spinning Top',
                signal: 'neutral',
                confidence: 1 - (bodySize / range),
                description: 'Indecision pattern - market uncertainty'
            };
        }
        return null;
    }

    // Marubozu pattern detection
    checkMarubozu(candle) {
        const bodySize = Math.abs(candle.close - candle.open);
        const upperShadow = candle.high - Math.max(candle.open, candle.close);
        const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
        const range = candle.high - candle.low;

        if (range > 0 && bodySize > range * 0.95 && 
            upperShadow < range * 0.05 && lowerShadow < range * 0.05) {
            
            const signal = candle.close > candle.open ? 'bullish' : 'bearish';
            
            return {
                type: 'marubozu',
                name: 'Marubozu',
                signal: signal,
                confidence: bodySize / range,
                description: `Strong ${signal} sentiment - no shadows`
            };
        }
        return null;
    }

    // Dark Cloud Cover pattern detection
    checkDarkCloudCover(current, previous) {
        if (previous.close > previous.open && current.close < current.open &&
            current.open > previous.high && 
            current.close < (previous.open + previous.close) / 2) {
            
            return {
                type: 'dark_cloud_cover',
                name: 'Dark Cloud Cover',
                signal: 'bearish',
                confidence: 0.7,
                description: 'Bearish reversal pattern - selling pressure'
            };
        }
        return null;
    }

    // Update pattern display in UI
    updatePatternDisplay() {
        const patternContainer = document.getElementById('pattern-alerts');
        if (!patternContainer) return;

        // Clear existing patterns
        patternContainer.innerHTML = '';

        if (this.patterns.length === 0) {
            patternContainer.innerHTML = `
                <div class="col-12 text-center text-muted">
                    <i class="fas fa-search me-2"></i>
                    No patterns detected
                </div>
            `;
            return;
        }

        // Display recent patterns (last 5)
        const recentPatterns = this.patterns
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 5);

        recentPatterns.forEach(pattern => {
            const patternElement = this.createPatternElement(pattern);
            patternContainer.appendChild(patternElement);
        });
    }

    // Create pattern display element
    createPatternElement(pattern) {
        const div = document.createElement('div');
        div.className = 'col-lg-6 col-md-12 mb-2';
        
        const signalClass = pattern.signal === 'bullish' ? 'success' : 
                           pattern.signal === 'bearish' ? 'danger' : 'warning';
        
        const confidencePercent = Math.round(pattern.confidence * 100);
        
        div.innerHTML = `
            <div class="pattern-alert ${pattern.signal}">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">
                            <span class="badge bg-${signalClass} me-2">${pattern.symbol}</span>
                            ${pattern.name}
                        </h6>
                        <p class="mb-1 small">${pattern.description}</p>
                        <small class="text-muted">
                            ${pattern.date.toLocaleDateString()} • 
                            Confidence: ${confidencePercent}%
                        </small>
                    </div>
                    <div class="text-end">
                        <div class="h6 mb-0">${ConfigUtils.formatCurrency(pattern.price)}</div>
                        <small class="text-${signalClass}">${pattern.signal.toUpperCase()}</small>
                    </div>
                </div>
            </div>
        `;
        
        return div;
    }

    // Get patterns for specific symbol
    getPatternsForSymbol(symbol) {
        return this.patterns.filter(p => p.symbol === symbol);
    }

    // Get patterns by type
    getPatternsByType(type) {
        return this.patterns.filter(p => p.type === type);
    }

    // Get patterns by signal
    getPatternsBySignal(signal) {
        return this.patterns.filter(p => p.signal === signal);
    }
}

// Create global pattern detector instance
const patternDetector = new PatternDetector();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PatternDetector, patternDetector };
}
