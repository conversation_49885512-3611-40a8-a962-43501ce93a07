/* Custom CSS for Trading Dashboard */

/* Global Styles */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

body {
    background-color: #f5f6fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* Market Cards */
.market-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.market-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.market-card .price {
    font-weight: 700;
    color: var(--dark-color);
}

.market-card .change {
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
}

.change.positive {
    background-color: #d4edda;
    color: var(--success-color);
}

.change.negative {
    background-color: #f8d7da;
    color: var(--danger-color);
}

/* Chart Container */
#tradingview_chart {
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* Pattern Alerts */
.pattern-alert {
    border-left: 4px solid var(--primary-color);
    background-color: #f8f9fa;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.pattern-alert.bullish {
    border-left-color: var(--success-color);
}

.pattern-alert.bearish {
    border-left-color: var(--danger-color);
}

.pattern-alert.neutral {
    border-left-color: var(--warning-color);
}

/* Backtest Results */
.metric h4, .metric h6 {
    margin: 0;
    font-weight: 700;
}

.metric .positive {
    color: var(--success-color);
}

.metric .negative {
    color: var(--danger-color);
}

/* Strategy Parameters */
#strategy-params {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    display: none;
}

#strategy-params.show {
    display: block;
}

/* Position Size Slider */
#position-size-value {
    font-weight: 600;
    color: var(--primary-color);
}

/* Trade History Table */
.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.trade-buy {
    color: var(--success-color);
    font-weight: 600;
}

.trade-sell {
    color: var(--danger-color);
    font-weight: 600;
}

.profit {
    color: var(--success-color);
    font-weight: 600;
}

.loss {
    color: var(--danger-color);
    font-weight: 600;
}

/* Learning Section */
.pattern-btn, .indicator-btn {
    transition: all 0.2s ease;
}

.pattern-btn:hover, .indicator-btn:hover {
    transform: translateY(-1px);
}

.pattern-btn.active, .indicator-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Pattern Content */
.pattern-illustration {
    text-align: center;
    margin: 20px 0;
}

.pattern-illustration svg {
    max-width: 100%;
    height: auto;
}

.pattern-description {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: var(--border-radius);
    margin: 15px 0;
}

.pattern-signals {
    background-color: #e3f2fd;
    padding: 15px;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--info-color);
}

/* Quiz Styles */
.quiz-option {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    padding: 15px;
    margin: 10px 0;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
}

.quiz-option:hover {
    background-color: #e9ecef;
    border-color: var(--primary-color);
}

.quiz-option.selected {
    background-color: #e3f2fd;
    border-color: var(--primary-color);
}

.quiz-option.correct {
    background-color: #d4edda;
    border-color: var(--success-color);
}

.quiz-option.incorrect {
    background-color: #f8d7da;
    border-color: var(--danger-color);
}

.quiz-explanation {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 15px;
    border-radius: var(--border-radius);
    margin-top: 15px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .market-card {
        margin-bottom: 15px;
    }
    
    #tradingview_chart {
        height: 400px !important;
    }
    
    .btn-group .btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .metric h4 {
        font-size: 1.25rem;
    }
    
    .btn-group {
        flex-wrap: wrap;
    }
    
    .btn-group .btn {
        margin-bottom: 5px;
    }
}

/* Utility Classes */
.text-positive {
    color: var(--success-color) !important;
}

.text-negative {
    color: var(--danger-color) !important;
}

.bg-light-success {
    background-color: #d4edda !important;
}

.bg-light-danger {
    background-color: #f8d7da !important;
}

.border-success {
    border-color: var(--success-color) !important;
}

.border-danger {
    border-color: var(--danger-color) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
