// Chart Integration Module for Trading Dashboard

class ChartManager {
    constructor() {
        this.widget = null;
        this.currentSymbol = 'NASDAQ:AAPL';
        this.isInitialized = false;
        this.patternOverlays = [];
    }

    // Initialize TradingView widget
    init(containerId = 'tradingview_chart') {
        if (typeof TradingView === 'undefined') {
            console.error('TradingView library not loaded');
            return;
        }

        const widgetConfig = {
            ...CONFIG.TRADINGVIEW.WIDGET_CONFIG,
            container_id: containerId,
            symbol: this.currentSymbol
        };

        try {
            this.widget = new TradingView.widget(widgetConfig);
            this.isInitialized = true;
            
            // Set up event listeners
            this.setupEventListeners();
            
            ConfigUtils.debug('TradingView widget initialized');
        } catch (error) {
            console.error('Failed to initialize TradingView widget:', error);
        }
    }

    // Set up widget event listeners
    setupEventListeners() {
        if (!this.widget) return;

        // Widget ready event
        this.widget.onChartReady(() => {
            ConfigUtils.debug('TradingView chart ready');
            
            // Get chart object
            this.chart = this.widget.chart();
            
            // Set up chart event listeners
            this.chart.onSymbolChanged().subscribe(null, (symbolInfo) => {
                this.currentSymbol = symbolInfo.name;
                ConfigUtils.debug('Symbol changed to:', this.currentSymbol);
                this.onSymbolChange(symbolInfo);
            });

            this.chart.onIntervalChanged().subscribe(null, (interval) => {
                ConfigUtils.debug('Interval changed to:', interval);
                this.onIntervalChange(interval);
            });
        });
    }

    // Change chart symbol
    changeSymbol(symbol) {
        if (!this.widget || !this.isInitialized) {
            console.error('Chart not initialized');
            return;
        }

        // Convert symbol to TradingView format
        const tvSymbol = this.convertToTradingViewSymbol(symbol);
        
        try {
            this.widget.setSymbol(tvSymbol, () => {
                this.currentSymbol = tvSymbol;
                ConfigUtils.debug('Symbol changed to:', tvSymbol);
            });
        } catch (error) {
            console.error('Failed to change symbol:', error);
        }
    }

    // Convert symbol to TradingView format
    convertToTradingViewSymbol(symbol) {
        const symbolMappings = {
            'AAPL': 'NASDAQ:AAPL',
            'GOOGL': 'NASDAQ:GOOGL',
            'MSFT': 'NASDAQ:MSFT',
            'TSLA': 'NASDAQ:TSLA',
            'AMZN': 'NASDAQ:AMZN',
            'BTCUSD': 'BINANCE:BTCUSDT',
            'ETHUSD': 'BINANCE:ETHUSDT',
            'BTC': 'BINANCE:BTCUSDT',
            'ETH': 'BINANCE:ETHUSDT'
        };

        return symbolMappings[symbol] || `NASDAQ:${symbol}`;
    }

    // Change chart interval
    changeInterval(interval) {
        if (!this.widget || !this.isInitialized) {
            console.error('Chart not initialized');
            return;
        }

        try {
            this.widget.setInterval(interval, () => {
                ConfigUtils.debug('Interval changed to:', interval);
            });
        } catch (error) {
            console.error('Failed to change interval:', error);
        }
    }

    // Add study/indicator to chart
    addStudy(studyName, inputs = {}) {
        if (!this.chart) {
            console.error('Chart not ready');
            return;
        }

        try {
            this.chart.createStudy(studyName, false, false, inputs);
            ConfigUtils.debug('Study added:', studyName);
        } catch (error) {
            console.error('Failed to add study:', error);
        }
    }

    // Remove all studies
    removeAllStudies() {
        if (!this.chart) {
            console.error('Chart not ready');
            return;
        }

        try {
            this.chart.removeAllStudies();
            ConfigUtils.debug('All studies removed');
        } catch (error) {
            console.error('Failed to remove studies:', error);
        }
    }

    // Add pattern overlay to chart
    addPatternOverlay(pattern) {
        if (!this.chart) {
            console.error('Chart not ready');
            return;
        }

        try {
            const shape = this.chart.createShape(
                { time: pattern.time, price: pattern.price },
                {
                    shape: 'icon',
                    icon: this.getPatternIcon(pattern.type),
                    text: pattern.name,
                    color: this.getPatternColor(pattern.signal)
                }
            );

            this.patternOverlays.push({
                id: shape.id,
                pattern: pattern,
                shape: shape
            });

            ConfigUtils.debug('Pattern overlay added:', pattern);
        } catch (error) {
            console.error('Failed to add pattern overlay:', error);
        }
    }

    // Get icon for pattern type
    getPatternIcon(patternType) {
        const iconMap = {
            'doji': '⚡',
            'hammer': '🔨',
            'shooting_star': '⭐',
            'engulfing': '🔄',
            'harami': '🤱',
            'spinning_top': '🌪️',
            'marubozu': '📏',
            'dark_cloud_cover': '☁️'
        };

        return iconMap[patternType] || '📊';
    }

    // Get color for pattern signal
    getPatternColor(signal) {
        const colorMap = {
            'bullish': CONFIG.CHART_COLORS.BULLISH,
            'bearish': CONFIG.CHART_COLORS.BEARISH,
            'neutral': '#ffc107'
        };

        return colorMap[signal] || '#6c757d';
    }

    // Clear all pattern overlays
    clearPatternOverlays() {
        if (!this.chart) return;

        this.patternOverlays.forEach(overlay => {
            try {
                this.chart.removeEntity(overlay.shape);
            } catch (error) {
                console.error('Failed to remove pattern overlay:', error);
            }
        });

        this.patternOverlays = [];
        ConfigUtils.debug('Pattern overlays cleared');
    }

    // Event handlers
    onSymbolChange(symbolInfo) {
        // Emit custom event for other components
        const event = new CustomEvent('chartSymbolChanged', {
            detail: { symbolInfo, currentSymbol: this.currentSymbol }
        });
        document.dispatchEvent(event);
    }

    onIntervalChange(interval) {
        // Emit custom event for other components
        const event = new CustomEvent('chartIntervalChanged', {
            detail: { interval }
        });
        document.dispatchEvent(event);
    }

    // Take screenshot of chart
    takeScreenshot() {
        if (!this.widget) {
            console.error('Chart not initialized');
            return;
        }

        try {
            this.widget.takeScreenshot().then((canvas) => {
                // Convert canvas to blob and download
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `chart_${this.currentSymbol}_${Date.now()}.png`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                });
            });
        } catch (error) {
            console.error('Failed to take screenshot:', error);
        }
    }

    // Get current chart data
    getCurrentData() {
        if (!this.chart) {
            console.error('Chart not ready');
            return null;
        }

        try {
            // This would require TradingView's advanced API features
            // For now, return basic info
            return {
                symbol: this.currentSymbol,
                interval: this.widget.getInterval(),
                timezone: this.widget.getTimezone()
            };
        } catch (error) {
            console.error('Failed to get chart data:', error);
            return null;
        }
    }

    // Resize chart
    resize() {
        if (!this.widget) return;

        try {
            this.widget.resize();
            ConfigUtils.debug('Chart resized');
        } catch (error) {
            console.error('Failed to resize chart:', error);
        }
    }

    // Destroy widget
    destroy() {
        if (this.widget) {
            try {
                this.widget.remove();
                this.widget = null;
                this.chart = null;
                this.isInitialized = false;
                this.patternOverlays = [];
                ConfigUtils.debug('Chart destroyed');
            } catch (error) {
                console.error('Failed to destroy chart:', error);
            }
        }
    }
}

// Chart utilities
class ChartUtils {
    // Create simple candlestick chart using Chart.js
    static createCandlestickChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            console.error('Canvas element not found:', canvasId);
            return null;
        }

        // Prepare data for Chart.js
        const chartData = {
            datasets: [{
                label: 'Price',
                data: data.map(item => ({
                    x: item.date,
                    o: item.open,
                    h: item.high,
                    l: item.low,
                    c: item.close
                })),
                borderColor: CONFIG.CHART_COLORS.BULLISH,
                backgroundColor: CONFIG.CHART_COLORS.BEARISH
            }]
        };

        const config = {
            type: 'candlestick',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day'
                        }
                    },
                    y: {
                        beginAtZero: false
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                ...options
            }
        };

        try {
            return new Chart(ctx, config);
        } catch (error) {
            console.error('Failed to create candlestick chart:', error);
            return null;
        }
    }

    // Create line chart for performance tracking
    static createLineChart(canvasId, data, label = 'Value', options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            console.error('Canvas element not found:', canvasId);
            return null;
        }

        const config = {
            type: 'line',
            data: {
                labels: data.map(item => item.date),
                datasets: [{
                    label: label,
                    data: data.map(item => item.value),
                    borderColor: CONFIG.CHART_COLORS.BULLISH,
                    backgroundColor: CONFIG.CHART_COLORS.BULLISH + '20',
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day'
                        }
                    },
                    y: {
                        beginAtZero: false
                    }
                },
                plugins: {
                    legend: {
                        display: true
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                ...options
            }
        };

        try {
            return new Chart(ctx, config);
        } catch (error) {
            console.error('Failed to create line chart:', error);
            return null;
        }
    }
}

// Create global chart manager instance
const chartManager = new ChartManager();

// Auto-resize chart on window resize
window.addEventListener('resize', () => {
    chartManager.resize();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ChartManager, ChartUtils, chartManager };
}
