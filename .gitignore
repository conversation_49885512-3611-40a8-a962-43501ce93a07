# API Keys and sensitive configuration
config.js
api-keys.js
.env
.env.local
.env.production

# Node.js dependencies (if using Node.js backend later)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python dependencies (if using Flask backend later)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Build directories
build/
dist/

# Temporary files
tmp/
temp/

# Database files (for future backend integration)
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Cache files
.cache/
.parcel-cache/

# Firebase
.firebase/
firebase-debug.log

# Netlify
.netlify/

# Local development
.local/
