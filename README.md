# Personal Finance Trading Dashboard

A comprehensive web-based trading dashboard built with HTML, CSS, and JavaScript featuring live market data, interactive charts, candlestick pattern recognition, backtesting capabilities, and educational tools.

## 🚀 Features

### 📊 Live Market Data
- Real-time stock prices via Alpha Vantage API
- Live cryptocurrency data via CoinGecko API
- Automatic price updates every 30 seconds
- Support for major stocks (AAPL, GOOGL, MSFT, TSLA, AMZN)
- Popular cryptocurrencies (BTC, ETH, BNB, ADA, SOL)

### 📈 Interactive Charts
- TradingView widget integration
- Candlestick charts with technical indicators
- Multiple timeframes (1min, 5min, 1H, 1D)
- Technical indicators: RSI, MACD, Bollinger Bands
- Symbol switching and chart customization

### 🔍 Pattern Recognition
- Automatic candlestick pattern detection
- Supported patterns: <PERSON><PERSON>, <PERSON>, Shooting Star, Engulfing, Harami
- Real-time pattern alerts with confidence levels
- Pattern explanations and trading signals

### 🔄 Backtesting Engine
- Strategy backtesting with historical data
- Multiple built-in strategies (RSI, MA Crossover, Bollinger Bands, MACD)
- Performance metrics and trade analysis
- Visual performance charts
- Export trade history to CSV

### 🎓 Educational Tools
- Interactive learning modules
- Candlestick pattern explanations
- Technical indicator tutorials
- Knowledge quiz with flashcards
- Pattern recognition training

### 📱 Responsive Design
- Mobile-friendly interface
- Bootstrap-based styling
- Dark/light theme support
- Intuitive navigation

## 🛠️ Setup Instructions

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection for API calls
- Text editor (VS Code, Sublime Text, etc.)

### 1. Clone or Download
```bash
git clone <repository-url>
cd trading-dashboard
```

### 2. API Keys Setup
1. **Alpha Vantage API** (for stock data):
   - Visit [Alpha Vantage](https://www.alphavantage.co/support/#api-key)
   - Sign up for a free API key
   - Open `js/config.js`
   - Replace `YOUR_ALPHA_VANTAGE_API_KEY` with your actual API key

2. **CoinGecko API** (for crypto data):
   - No API key required for basic usage
   - Rate limited to 50 calls/minute

### 3. Local Development
For local development, you can simply open `index.html` in your browser. However, for full functionality (especially API calls), it's recommended to use a local server:

#### Option A: Python Server
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### Option B: Node.js Server
```bash
# Install http-server globally
npm install -g http-server

# Start server
http-server -p 8000
```

#### Option C: VS Code Live Server
1. Install "Live Server" extension in VS Code
2. Right-click on `index.html`
3. Select "Open with Live Server"

### 4. Access the Application
Open your browser and navigate to:
- `http://localhost:8000` (if using local server)
- Or directly open `index.html` in your browser

## 📁 Project Structure

```
trading-dashboard/
├── index.html              # Main dashboard page
├── backtest.html           # Backtesting interface
├── learn.html              # Educational tools
├── README.md               # This file
├── css/
│   └── style.css           # Custom styles
├── js/
│   ├── config.js           # Configuration and API keys
│   ├── api.js              # API integration (Alpha Vantage, CoinGecko)
│   ├── chart.js            # TradingView chart integration
│   ├── dashboard.js        # Main dashboard functionality
│   ├── patterns.js         # Candlestick pattern detection
│   ├── backtest-engine.js  # Backtesting logic (to be created)
│   ├── backtest.js         # Backtest UI (to be created)
│   ├── learn-data.js       # Educational content (to be created)
│   └── learn.js            # Learning interface (to be created)
└── assets/                 # Images and icons (to be added)
```

## 🔧 Configuration

### API Settings
Edit `js/config.js` to customize:
- API keys and endpoints
- Update intervals
- Default symbols to track
- Chart colors and styling
- Pattern detection sensitivity

### Adding New Symbols
To add new stocks or cryptocurrencies:

1. **Stocks**: Add to `CONFIG.SYMBOLS.STOCKS` in `config.js`
```javascript
{ symbol: 'NVDA', name: 'NVIDIA Corp.', exchange: 'NASDAQ' }
```

2. **Crypto**: Add to `CONFIG.SYMBOLS.CRYPTO` in `config.js`
```javascript
{ id: 'chainlink', symbol: 'LINK', name: 'Chainlink' }
```

## 🚀 Usage

### Dashboard
1. View live market data for stocks and cryptocurrencies
2. Click on market cards to change the chart symbol
3. Use chart buttons to switch between different assets
4. Monitor detected candlestick patterns in real-time

### Backtesting
1. Navigate to the "Backtest" tab
2. Select a symbol and date range
3. Choose a trading strategy
4. Configure parameters and initial capital
5. Run the backtest to see results

### Learning
1. Go to the "Learn" tab
2. Explore candlestick patterns and technical indicators
3. Take the quiz to test your knowledge
4. Use interactive examples to understand concepts

## 🔑 Keyboard Shortcuts
- `Ctrl/Cmd + R`: Refresh all data
- `Ctrl/Cmd + E`: Export current data

## 🌐 Deployment

### GitHub Pages
1. Push your code to a GitHub repository
2. Go to repository Settings > Pages
3. Select source branch (usually `main`)
4. Your app will be available at `https://username.github.io/repository-name`

### Firebase Hosting
1. Install Firebase CLI: `npm install -g firebase-tools`
2. Initialize Firebase: `firebase init hosting`
3. Deploy: `firebase deploy`

### Netlify
1. Connect your GitHub repository to Netlify
2. Set build command to empty (static site)
3. Set publish directory to root (`/`)
4. Deploy automatically on git push

## 🔮 Future Enhancements

### Backend Integration (Optional)
The current structure is ready for backend integration:

#### Flask Backend (Python)
- User authentication and portfolios
- Advanced backtesting with more data
- Custom strategy builder
- Real-time alerts and notifications

#### Node.js Backend
- WebSocket connections for real-time data
- Database integration for historical analysis
- API rate limiting and caching
- User preference storage

### Planned Features
- [ ] Portfolio tracking
- [ ] Custom alerts and notifications
- [ ] Advanced charting tools
- [ ] Social trading features
- [ ] Mobile app (React Native/Flutter)
- [ ] Machine learning predictions

## 🐛 Troubleshooting

### Common Issues

1. **API Rate Limits**
   - Alpha Vantage: 5 calls/minute, 500 calls/day (free tier)
   - Solution: Implement caching or upgrade to premium

2. **CORS Errors**
   - Use a local server instead of opening HTML directly
   - Consider using a CORS proxy for development

3. **Chart Not Loading**
   - Check internet connection
   - Verify TradingView widget script is loaded
   - Check browser console for errors

4. **No Data Displayed**
   - Verify API keys are correctly set
   - Check network connectivity
   - Review browser console for API errors

### Debug Mode
Enable debug mode in `config.js`:
```javascript
DEBUG_MODE: true
```

This will show detailed logs in the browser console.

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For questions or issues:
- Check the troubleshooting section
- Review browser console for errors
- Create an issue on GitHub

## 🙏 Acknowledgments

- [Alpha Vantage](https://www.alphavantage.co/) for stock market data
- [CoinGecko](https://www.coingecko.com/) for cryptocurrency data
- [TradingView](https://www.tradingview.com/) for charting widgets
- [Bootstrap](https://getbootstrap.com/) for responsive design
- [Chart.js](https://www.chartjs.org/) for additional charting capabilities
