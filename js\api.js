// API Integration Module for Trading Dashboard

class APIManager {
    constructor() {
        this.cache = new Map();
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.rateLimitDelay = 1000; // 1 second between requests
    }

    // Generic API request with error handling and caching
    async makeRequest(url, options = {}) {
        const cacheKey = url + JSON.stringify(options);
        const cached = this.cache.get(cacheKey);
        
        // Return cached data if it's less than 5 minutes old
        if (cached && Date.now() - cached.timestamp < 300000) {
            ConfigUtils.debug('Returning cached data for:', url);
            return cached.data;
        }

        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            // Cache the response
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });

            return data;
        } catch (error) {
            ConfigUtils.debug('API request failed:', error);
            throw error;
        }
    }

    // Add request to queue to handle rate limiting
    async queueRequest(requestFn) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({ requestFn, resolve, reject });
            this.processQueue();
        });
    }

    // Process request queue with rate limiting
    async processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        while (this.requestQueue.length > 0) {
            const { requestFn, resolve, reject } = this.requestQueue.shift();
            
            try {
                const result = await requestFn();
                resolve(result);
            } catch (error) {
                reject(error);
            }

            // Wait before processing next request
            if (this.requestQueue.length > 0) {
                await new Promise(resolve => setTimeout(resolve, this.rateLimitDelay));
            }
        }

        this.isProcessingQueue = false;
    }
}

// Stock Data API (Alpha Vantage)
class StockAPI extends APIManager {
    constructor() {
        super();
        this.apiKey = ConfigUtils.getApiKey('ALPHA_VANTAGE');
        this.baseUrl = CONFIG.ALPHA_VANTAGE.BASE_URL;
    }

    // Get real-time stock quote
    async getQuote(symbol) {
        const url = `${this.baseUrl}?function=${CONFIG.ALPHA_VANTAGE.ENDPOINTS.QUOTE}&symbol=${symbol}&apikey=${this.apiKey}`;
        
        return this.queueRequest(async () => {
            const data = await this.makeRequest(url);
            
            if (data['Error Message']) {
                throw new Error(CONFIG.ERROR_MESSAGES.INVALID_SYMBOL);
            }
            
            if (data['Note']) {
                throw new Error(CONFIG.ERROR_MESSAGES.API_LIMIT);
            }

            const quote = data['Global Quote'];
            if (!quote) {
                throw new Error(CONFIG.ERROR_MESSAGES.NO_DATA);
            }

            return {
                symbol: quote['01. symbol'],
                price: parseFloat(quote['05. price']),
                change: parseFloat(quote['09. change']),
                changePercent: parseFloat(quote['10. change percent'].replace('%', '')),
                volume: parseInt(quote['06. volume']),
                previousClose: parseFloat(quote['08. previous close']),
                timestamp: new Date(quote['07. latest trading day'])
            };
        });
    }

    // Get historical data for backtesting
    async getHistoricalData(symbol, interval = 'daily', outputSize = 'compact') {
        let functionName;
        switch (interval) {
            case '1min':
            case '5min':
            case '15min':
            case '30min':
            case '60min':
                functionName = CONFIG.ALPHA_VANTAGE.ENDPOINTS.INTRADAY;
                break;
            case 'daily':
                functionName = CONFIG.ALPHA_VANTAGE.ENDPOINTS.DAILY;
                break;
            case 'weekly':
                functionName = CONFIG.ALPHA_VANTAGE.ENDPOINTS.WEEKLY;
                break;
            case 'monthly':
                functionName = CONFIG.ALPHA_VANTAGE.ENDPOINTS.MONTHLY;
                break;
            default:
                functionName = CONFIG.ALPHA_VANTAGE.ENDPOINTS.DAILY;
        }

        let url = `${this.baseUrl}?function=${functionName}&symbol=${symbol}&outputsize=${outputSize}&apikey=${this.apiKey}`;
        
        if (functionName === CONFIG.ALPHA_VANTAGE.ENDPOINTS.INTRADAY) {
            url += `&interval=${interval}`;
        }

        return this.queueRequest(async () => {
            const data = await this.makeRequest(url);
            
            if (data['Error Message']) {
                throw new Error(CONFIG.ERROR_MESSAGES.INVALID_SYMBOL);
            }
            
            if (data['Note']) {
                throw new Error(CONFIG.ERROR_MESSAGES.API_LIMIT);
            }

            // Extract time series data
            const timeSeriesKey = Object.keys(data).find(key => key.includes('Time Series'));
            const timeSeries = data[timeSeriesKey];
            
            if (!timeSeries) {
                throw new Error(CONFIG.ERROR_MESSAGES.NO_DATA);
            }

            // Convert to array format
            const historicalData = Object.entries(timeSeries).map(([date, values]) => ({
                date: new Date(date),
                open: parseFloat(values['1. open']),
                high: parseFloat(values['2. high']),
                low: parseFloat(values['3. low']),
                close: parseFloat(values['4. close']),
                volume: parseInt(values['5. volume'])
            })).sort((a, b) => a.date - b.date);

            return historicalData;
        });
    }
}

// Cryptocurrency API (CoinGecko)
class CryptoAPI extends APIManager {
    constructor() {
        super();
        this.baseUrl = CONFIG.COINGECKO.BASE_URL;
    }

    // Get real-time crypto prices
    async getPrices(coinIds) {
        const ids = Array.isArray(coinIds) ? coinIds.join(',') : coinIds;
        const url = `${this.baseUrl}${CONFIG.COINGECKO.ENDPOINTS.SIMPLE_PRICE}?ids=${ids}&vs_currencies=usd&include_24hr_change=true&include_24hr_vol=true`;
        
        return this.queueRequest(async () => {
            const data = await this.makeRequest(url);
            
            // Transform data to consistent format
            const prices = {};
            Object.entries(data).forEach(([coinId, priceData]) => {
                prices[coinId] = {
                    price: priceData.usd,
                    change24h: priceData.usd_24h_change || 0,
                    volume24h: priceData.usd_24h_vol || 0,
                    timestamp: new Date()
                };
            });

            return prices;
        });
    }

    // Get historical crypto data
    async getHistoricalData(coinId, days = 30, interval = 'daily') {
        const url = `${this.baseUrl}/coins/${coinId}/market_chart?vs_currency=usd&days=${days}&interval=${interval}`;
        
        return this.queueRequest(async () => {
            const data = await this.makeRequest(url);
            
            if (!data.prices) {
                throw new Error(CONFIG.ERROR_MESSAGES.NO_DATA);
            }

            // Convert to OHLCV format (approximate from price data)
            const historicalData = data.prices.map((pricePoint, index) => {
                const [timestamp, price] = pricePoint;
                const volume = data.total_volumes[index] ? data.total_volumes[index][1] : 0;
                
                return {
                    date: new Date(timestamp),
                    open: price,
                    high: price,
                    low: price,
                    close: price,
                    volume: volume
                };
            });

            return historicalData;
        });
    }

    // Get list of available cryptocurrencies
    async getCoinsList() {
        const url = `${this.baseUrl}/coins/list`;
        
        return this.queueRequest(async () => {
            const data = await this.makeRequest(url);
            return data.slice(0, 100); // Return top 100 coins
        });
    }
}

// Market Data Manager
class MarketDataManager {
    constructor() {
        this.stockAPI = new StockAPI();
        this.cryptoAPI = new CryptoAPI();
        this.subscribers = new Map();
        this.updateIntervals = new Map();
    }

    // Subscribe to real-time updates
    subscribe(symbol, callback, interval = CONFIG.UPDATE_INTERVALS.LIVE_DATA) {
        if (!this.subscribers.has(symbol)) {
            this.subscribers.set(symbol, []);
        }
        
        this.subscribers.get(symbol).push(callback);
        
        // Start update interval if not already running
        if (!this.updateIntervals.has(symbol)) {
            const intervalId = setInterval(() => {
                this.updateSymbol(symbol);
            }, interval);
            
            this.updateIntervals.set(symbol, intervalId);
            
            // Initial update
            this.updateSymbol(symbol);
        }
    }

    // Unsubscribe from updates
    unsubscribe(symbol, callback) {
        if (this.subscribers.has(symbol)) {
            const callbacks = this.subscribers.get(symbol);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
            
            // Stop updates if no more subscribers
            if (callbacks.length === 0) {
                this.subscribers.delete(symbol);
                const intervalId = this.updateIntervals.get(symbol);
                if (intervalId) {
                    clearInterval(intervalId);
                    this.updateIntervals.delete(symbol);
                }
            }
        }
    }

    // Update symbol data and notify subscribers
    async updateSymbol(symbol) {
        try {
            let data;
            
            // Determine if it's a stock or crypto symbol
            const cryptoSymbol = CONFIG.SYMBOLS.CRYPTO.find(c => c.id === symbol);
            
            if (cryptoSymbol) {
                const prices = await this.cryptoAPI.getPrices(symbol);
                data = prices[symbol];
            } else {
                data = await this.stockAPI.getQuote(symbol);
            }
            
            // Notify all subscribers
            const callbacks = this.subscribers.get(symbol) || [];
            callbacks.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    ConfigUtils.debug('Error in subscriber callback:', error);
                }
            });
            
        } catch (error) {
            ConfigUtils.debug(`Error updating ${symbol}:`, error);
            
            // Notify subscribers of error
            const callbacks = this.subscribers.get(symbol) || [];
            callbacks.forEach(callback => {
                try {
                    callback(null, error);
                } catch (callbackError) {
                    ConfigUtils.debug('Error in error callback:', callbackError);
                }
            });
        }
    }

    // Get historical data for any symbol
    async getHistoricalData(symbol, period = '1M', interval = 'daily') {
        const cryptoSymbol = CONFIG.SYMBOLS.CRYPTO.find(c => c.id === symbol);
        
        if (cryptoSymbol) {
            const days = this.periodToDays(period);
            return await this.cryptoAPI.getHistoricalData(symbol, days, interval);
        } else {
            return await this.stockAPI.getHistoricalData(symbol, interval);
        }
    }

    // Convert period string to days
    periodToDays(period) {
        const periodMap = {
            '1D': 1,
            '1W': 7,
            '1M': 30,
            '3M': 90,
            '6M': 180,
            '1Y': 365,
            '2Y': 730
        };
        
        return periodMap[period] || 30;
    }

    // Cleanup all subscriptions
    cleanup() {
        this.updateIntervals.forEach(intervalId => clearInterval(intervalId));
        this.updateIntervals.clear();
        this.subscribers.clear();
    }
}

// Create global instance
const marketData = new MarketDataManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { StockAPI, CryptoAPI, MarketDataManager, marketData };
}
