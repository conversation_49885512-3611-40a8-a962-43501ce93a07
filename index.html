<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Finance Trading Dashboard</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-chart-line me-2"></i>
                Trading Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="backtest.html">
                            <i class="fas fa-history me-1"></i>Backtest
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="learn.html">
                            <i class="fas fa-graduation-cap me-1"></i>Learn
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Market Overview Cards -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="mb-3">Market Overview</h2>
            </div>
        </div>
        
        <!-- Live Data Cards -->
        <div class="row mb-4" id="market-cards">
            <!-- Stock Cards -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card market-card" data-symbol="AAPL">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">AAPL</h6>
                                <small class="text-muted">Apple Inc.</small>
                            </div>
                            <i class="fas fa-chart-line text-primary"></i>
                        </div>
                        <div class="mt-2">
                            <h4 class="price mb-1" id="price-AAPL">Loading...</h4>
                            <span class="change" id="change-AAPL">--</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card market-card" data-symbol="GOOGL">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">GOOGL</h6>
                                <small class="text-muted">Alphabet Inc.</small>
                            </div>
                            <i class="fas fa-chart-line text-primary"></i>
                        </div>
                        <div class="mt-2">
                            <h4 class="price mb-1" id="price-GOOGL">Loading...</h4>
                            <span class="change" id="change-GOOGL">--</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Crypto Cards -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card market-card" data-symbol="bitcoin">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">BTC</h6>
                                <small class="text-muted">Bitcoin</small>
                            </div>
                            <i class="fab fa-bitcoin text-warning"></i>
                        </div>
                        <div class="mt-2">
                            <h4 class="price mb-1" id="price-bitcoin">Loading...</h4>
                            <span class="change" id="change-bitcoin">--</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card market-card" data-symbol="ethereum">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">ETH</h6>
                                <small class="text-muted">Ethereum</small>
                            </div>
                            <i class="fab fa-ethereum text-info"></i>
                        </div>
                        <div class="mt-2">
                            <h4 class="price mb-1" id="price-ethereum">Loading...</h4>
                            <span class="change" id="change-ethereum">--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chart Section -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Interactive Chart</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm active" data-symbol="AAPL">AAPL</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" data-symbol="GOOGL">GOOGL</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" data-symbol="BTCUSD">BTC</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" data-symbol="ETHUSD">ETH</button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- TradingView Widget -->
                        <div id="tradingview_chart" style="height: 600px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pattern Detection Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            Detected Patterns
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="pattern-alerts" class="row">
                            <!-- Pattern alerts will be populated by JavaScript -->
                            <div class="col-12 text-center text-muted">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Analyzing patterns...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- TradingView Widget -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <!-- Custom JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/chart.js"></script>
    <script src="js/patterns.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
