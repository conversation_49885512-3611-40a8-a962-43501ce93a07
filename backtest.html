<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backtesting - Trading Dashboard</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-chart-line me-2"></i>
                Trading Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="backtest.html">
                            <i class="fas fa-history me-1"></i>Backtest
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="learn.html">
                            <i class="fas fa-graduation-cap me-1"></i>Learn
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Strategy Configuration Panel -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            Strategy Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="backtest-form">
                            <!-- Symbol Selection -->
                            <div class="mb-3">
                                <label for="symbol" class="form-label">Symbol</label>
                                <select class="form-select" id="symbol" required>
                                    <option value="">Select Symbol</option>
                                    <option value="AAPL">AAPL - Apple Inc.</option>
                                    <option value="GOOGL">GOOGL - Alphabet Inc.</option>
                                    <option value="MSFT">MSFT - Microsoft Corp.</option>
                                    <option value="TSLA">TSLA - Tesla Inc.</option>
                                    <option value="AMZN">AMZN - Amazon.com Inc.</option>
                                </select>
                            </div>

                            <!-- Date Range -->
                            <div class="row mb-3">
                                <div class="col-6">
                                    <label for="start-date" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="start-date" required>
                                </div>
                                <div class="col-6">
                                    <label for="end-date" class="form-label">End Date</label>
                                    <input type="date" class="form-control" id="end-date" required>
                                </div>
                            </div>

                            <!-- Strategy Selection -->
                            <div class="mb-3">
                                <label for="strategy" class="form-label">Strategy</label>
                                <select class="form-select" id="strategy" required>
                                    <option value="">Select Strategy</option>
                                    <option value="rsi">RSI Strategy (Buy <30, Sell >70)</option>
                                    <option value="ma_crossover">Moving Average Crossover</option>
                                    <option value="bollinger">Bollinger Bands</option>
                                    <option value="macd">MACD Strategy</option>
                                </select>
                            </div>

                            <!-- Strategy Parameters -->
                            <div id="strategy-params">
                                <!-- Dynamic parameters based on strategy selection -->
                            </div>

                            <!-- Initial Capital -->
                            <div class="mb-3">
                                <label for="initial-capital" class="form-label">Initial Capital ($)</label>
                                <input type="number" class="form-control" id="initial-capital" value="10000" min="1000" step="100" required>
                            </div>

                            <!-- Position Size -->
                            <div class="mb-3">
                                <label for="position-size" class="form-label">Position Size (%)</label>
                                <input type="range" class="form-range" id="position-size" min="10" max="100" value="25" step="5">
                                <div class="d-flex justify-content-between">
                                    <small>10%</small>
                                    <small id="position-size-value">25%</small>
                                    <small>100%</small>
                                </div>
                            </div>

                            <!-- Run Backtest Button -->
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-play me-2"></i>
                                Run Backtest
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Results Summary -->
                <div class="card mt-3" id="results-summary" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            Results Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="metric">
                                    <h4 id="total-return" class="mb-1">--</h4>
                                    <small class="text-muted">Total Return</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="metric">
                                    <h4 id="win-rate" class="mb-1">--</h4>
                                    <small class="text-muted">Win Rate</small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="metric">
                                    <h6 id="total-trades" class="mb-1">--</h6>
                                    <small class="text-muted">Trades</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="metric">
                                    <h6 id="max-drawdown" class="mb-1">--</h6>
                                    <small class="text-muted">Max DD</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="metric">
                                    <h6 id="sharpe-ratio" class="mb-1">--</h6>
                                    <small class="text-muted">Sharpe</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Panel -->
            <div class="col-lg-8">
                <!-- Performance Chart -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            Portfolio Performance
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="loading-chart" class="text-center py-5">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Configure and run a backtest to see results</p>
                        </div>
                        <canvas id="performance-chart" style="display: none;"></canvas>
                    </div>
                </div>

                <!-- Trade History -->
                <div class="card mt-3" id="trade-history-card" style="display: none;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Trade History
                        </h6>
                        <button class="btn btn-sm btn-outline-secondary" id="export-trades">
                            <i class="fas fa-download me-1"></i>Export CSV
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm" id="trades-table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Action</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>P&L</th>
                                        <th>Signal</th>
                                    </tr>
                                </thead>
                                <tbody id="trades-tbody">
                                    <!-- Trade rows will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/backtest-engine.js"></script>
    <script src="js/backtest.js"></script>
</body>
</html>
